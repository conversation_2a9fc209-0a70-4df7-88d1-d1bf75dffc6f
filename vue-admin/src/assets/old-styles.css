/* 原始样式适配 - 基础样式 */
body {
  margin: 0;
  padding: 0;
  background-image: url("/img/bg-main.png");
  background-size: cover;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
}

::-webkit-scrollbar-track {
  background: #fff;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 图标字体 */
@font-face {
  font-family: 'iconfont';
  src: url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff2?t=1752327680529') format('woff2'),
       url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.woff?t=1752327680529') format('woff'),
       url('//at.alicdn.com/t/c/font_4973147_b6g1ritc17.ttf?t=1752327680529') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 布局工具类 */
.flex-c-c {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex {
  display: flex;
}

.w-100 {
  width: 100%;
}

.flex-0 {
  flex: 0 0 auto;
}

.align-c {
  align-items: center;
}

.align-t {
  align-items: flex-start;
}

.align-b {
  align-items: flex-end;
}

.justify-l {
  justify-content: flex-start;
}

.justify-c {
  justify-content: center;
}

.justify-r {
  justify-content: flex-end;
}

.justify-y {
  justify-content: space-between;
}

.color1 {
  color: #c2c2c2;
}

/* 侧边栏样式 */
.sidebar {
  width: 150px;
  background-color: #ffffff;
  color: #000;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 48px;
  bottom: 20px;
  left: 10px;
  border-radius: 20px;
  padding-top: 10px;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar.collapsed .text3 span:nth-child(1) {
  display: none;
}

.sidebar.collapsed .user-info {
  justify-content: center;
}

.sidebar.collapsed .user-info .user-details {
  display: none;
}

.sidebar.collapsed .menu-container {
  padding-left: 8px;
  padding-right: 8px;
}

.sidebar.collapsed .menu-container .menu-item {
  justify-content: center;
  position: relative;
}

.sidebar.collapsed .menu-container .menu-item span {
  display: none;
}

.sidebar.collapsed .menu-container .menu-item i {
  margin-right: 0;
}

.sidebar.collapsed .menu-container .menu-item .message-count {
  position: absolute;
  right: 0;
  top: 0;
}

.sidebar .menu-container {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.sidebar .menu-container .line {
  height: 1px;
  background: #e6e6e6;
  margin: 30px 0;
}

.sidebar .menu-container .menu-item {
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 10px;
  cursor: pointer;
  transition: background-color 0.3s;
  white-space: nowrap;
  border-radius: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  text-decoration: none;
  color: inherit;
}

.sidebar .menu-container .menu-item i {
  font-size: 16px;
  margin-right: 8px;
  flex-shrink: 0;
}

.sidebar .menu-container .menu-item span {
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar .menu-container .menu-item:hover {
  background-color: #f5f5f5;
}

.sidebar .menu-container .menu-item.active,
.sidebar .menu-container .menu-item.router-link-active {
  background-color: #6C5DD3 !important;
  color: #fff !important;
}

.sidebar .menu-container .message-count {
  background-color: #FF754C;
  color: white;
  border-radius: 10px;
  padding: 0 6px;
  font-size: 12px;
  margin-left: auto;
  height: 20px;
  line-height: 20px;
  text-align: center;
}

.sidebar .text3 {
  background: #efecff;
  border-radius: 100px;
  padding: 4px 12px;
  text-align: center;
  margin: 0 12px 16px 12px;
  font-size: 12px;
  color: #7748F8;
}

.sidebar .user-info {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .user-info .user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #6C5DD3;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  color: #fff;
}

.sidebar .user-info .user-details {
  margin-left: 12px;
  white-space: nowrap;
  overflow: hidden;
}

.sidebar .user-info .user-details .user-name {
  font-weight: 500;
}

.sidebar .user-info .user-details .user-position {
  font-size: 12px;
  opacity: 0.8;
}

/* Header 样式 */
.header {
  padding: 12px 0;
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
}

.header .logo {
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16px;
  position: relative;
  z-index: 92;
}

.header .logo img {
  width: 28px;
  height: 28px;
  border-radius: 8px;
}

.header .logo span {
  margin-left: 12px;
  font-size: 20px;
  font-weight: bold;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.toggle-btn {
  position: relative;
  z-index: 92;
  cursor: pointer;
}

.toggle-btn i {
  color: #000;
  font-size: 18px;
  display: block;
}

.toggle-btn.collapsed i {
  transform: rotate(180deg);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  padding: 0 16px;
  overflow-y: auto;
  margin-left: 150px !important;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
  padding-top: 64px;
}

.main-content.collapsed {
  margin-left: 80px !important;
}

.main-content .body {
  flex: 1;
  margin-top: -14px;
}

/* 卡片样式 */
.main-card {
  box-shadow: 0 40px 60px 1px rgba(96, 106, 208, 0.1);
  border-radius: 24px;
  background: #fff;
  padding: 8px 12px 12px 12px;
}

.main-card .layui-card-body {
  padding: 0;
}

.main-card .layui-card-header {
  border-bottom: none;
  background: none;
  font-size: 15px;
  padding-left: 0;
  font-weight: 600;
  height: 32px;
  line-height: 35px;
}

/* 统计卡片 */
.ybp_card {
  padding: 16px;
  height: 118px;
  box-sizing: border-box;
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.ybp_card.v1 {
  background: #f3f1ff;
}

.ybp_card.v2 {
  background: #e4fcf7;
}

.ybp_card.v3 {
  background: #eaf2ff;
}

.ybp_card.v4 {
  background: #fff5e9;
}

.ybp_card .icon {
  width: 38px;
  height: 38px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 8px;
  margin-right: 8px;
}

.ybp_card .icon i {
  font-size: 20px;
  color: #6C5DD3;
}

.ybp_card .text1 {
  font-size: 12px;
}

.ybp_card .text2 {
  font-size: 18px;
  font-weight: bold;
}

/* 列表样式 */
.ybp_list .item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e6e6e6;
  font-size: 12px;
}

.ybp_list .item:last-child {
  border-bottom: none;
}

.ybp_list .item .text1 {
  width: 30px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
}

.ybp_list .item .head {
  width: 30px;
  height: 30px;
  border-radius: 100px;
  border: 2px solid #fff;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
  margin-right: 12px;
}

/* 表格样式 */
.qxjc_t4 {
  border-spacing: 0 6px;
  border-collapse: separate;
  margin: 0 !important;
}

.qxjc_t4 th,
.qxjc_t4 td {
  border: none;
  word-wrap: break-word;
  white-space: normal;
  position: relative;
  font-size: 13px;
}

.qxjc_t4 th:first-child,
.qxjc_t4 td:first-child {
  padding-left: 12px;
}

.qxjc_t4 th {
  color: #62687E;
  font-weight: 500;
  padding: 0 5px;
}

.qxjc_t4 tr td {
  border-top: 1px solid #E2E2E7;
  border-bottom: 1px solid #E2E2E7;
  color: #1B2441;
  padding: 9px 5px;
}

.qxjc_t4 tr td:first-child {
  border-left: 1px solid #E2E2E7;
  border-radius: 6px 0 0 6px;
}

.qxjc_t4 tr td:last-child {
  border-right: 1px solid #E2E2E7;
  border-radius: 0 6px 6px 0;
}

.qxjc_t4 tr:hover td {
  background: #f5f5f5;
}

.qxjc_t4.table-fixed {
  table-layout: fixed;
}

.qxjc_t4.table-fixed th,
.qxjc_t4.table-fixed td {
  white-space: nowrap;
}

/* Layui 栅格系统 */
.layui-row {
  display: flex;
  flex-wrap: wrap;
}

.layui-col-space10 > * {
  padding: 5px;
}

.layui-col-md3 {
  width: 25%;
}

.layui-col-md4 {
  width: 33.333333%;
}

.layui-col-md5 {
  width: 41.666667%;
}

.layui-col-md6 {
  width: 50%;
}

.layui-col-md8 {
  width: 66.666667%;
}

.layui-col-space15 > * {
  padding: 7.5px;
}

/* 卡片容器 */
.layui-card {
  border-radius: 24px;
  background-color: #fff;
  box-shadow: 0 1px 2px 0 rgba(0,0,0,.05);
}

.layui-card-header {
  position: relative;
  height: 42px;
  line-height: 42px;
  padding: 0 15px;
  border-bottom: 1px solid #f6f6f6;
  color: #333;
  border-radius: 2px 2px 0 0;
  font-size: 14px;
}

.layui-card-body {
  position: relative;
  padding: 10px 15px;
  line-height: 24px;
}

/* 省略号 */
.layui-elip {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

/* 表格 */
.layui-table {
  width: 100%;
  background-color: #fff;
  color: #666;
}

/* 暗色主题样式 */
body.dark-theme {
  background: #1a1a1a !important;
  background-image: none !important;
  color: #e0e0e0;
}

html.dark {
  background: #1a1a1a !important;
}

.app-layout {
  background: #f9fafb;
}

body.dark-theme .app-layout {
  background: #1a1a1a !important;
}

body.dark-theme .layui-table {
  background: none;
}

body.dark-theme .layui-card-header {
  color: #e0e0e0;
}

body.dark-theme .main-card {
  background: #2d2d2d !important;
  color: #e0e0e0;
}

body.dark-theme .main-content {
  background: #1a1a1a !important;
}

body.dark-theme .body {
  background: #1a1a1a !important;
}

body.dark-theme .ybp_card.v1 {
  background: #444253;
}

body.dark-theme .ybp_card.v2 {
  background: #3f4f4b;
}

body.dark-theme .ybp_card.v3 {
  background: #313743;
}

body.dark-theme .ybp_card.v4 {
  background: #4f3e29;
}

body.dark-theme .qxjc_t4 tr td {
  border-color: #444;
  color: #e0e0e0;
}

body.dark-theme .qxjc_t4 tr:hover td {
  background: #3a3a3a;
}

body.dark-theme .qxjc_t4 th {
  color: #b3b3b3;
}

body.dark-theme .ybp_list .item {
  border-bottom-color: #444;
}

/* Element Plus 暗色主题适配 */
body.dark-theme .el-card {
  background-color: #2d2d2d;
  border-color: #444;
}

body.dark-theme .el-input__wrapper {
  background-color: #3a3a3a;
  border-color: #555;
}

body.dark-theme .el-input__inner {
  color: #e0e0e0;
}

body.dark-theme .el-select .el-input__wrapper {
  background-color: #3a3a3a;
}

body.dark-theme .el-table {
  background-color: #2d2d2d;
  color: #e0e0e0;
}

body.dark-theme .el-table th.el-table__cell {
  background-color: #3a3a3a;
  color: #e0e0e0;
  border-color: #555;
}

body.dark-theme .el-table td.el-table__cell {
  border-color: #555;
}

body.dark-theme .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
  background-color: #333;
}

body.dark-theme .el-table__body tr:hover > td.el-table__cell {
  background-color: #404040;
}

body.dark-theme .el-pagination {
  color: #e0e0e0;
}

body.dark-theme .el-pagination .btn-prev,
body.dark-theme .el-pagination .btn-next {
  background-color: #3a3a3a;
  border-color: #555;
  color: #e0e0e0;
}

body.dark-theme .el-pagination .el-pager li {
  background-color: #3a3a3a;
  border-color: #555;
  color: #e0e0e0;
}

body.dark-theme .el-pagination .el-pager li:hover {
  color: #7748F8;
  border-color: #7748F8;
}

body.dark-theme .el-pagination .el-pager li.is-active {
  background-color: #7748F8;
  border-color: #7748F8;
  color: #fff;
}

body.dark-theme .sidebar {
  background-color: #2d2d2d !important;
  color: #e0e0e0;
}

body.dark-theme .sidebar .menu-container .menu-item:hover {
  background-color: #404040 !important;
}

body.dark-theme .sidebar .menu-container .menu-item.active {
  background-color: #6C5DD3 !important;
  color: #fff;
}

body.dark-theme .sidebar .text3 {
  background: #444253 !important;
  color: #e0e0e0;
}

body.dark-theme .sidebar .user-info {
  background-color: #2d2d2d !important;
  border-top-color: #444 !important;
}

body.dark-theme .sidebar .menu-container .line {
  background: #444 !important;
}

body.dark-theme .header .logo span {
  color: #e0e0e0;
}

body.dark-theme .toggle-btn i {
  color: #e0e0e0;
}

/* 强制暗色主题下表格文字为白色 */
body.dark-theme .el-table .el-table__cell,
body.dark-theme .el-table .el-table__cell *,
body.dark-theme .el-table p,
body.dark-theme .el-table span,
body.dark-theme .el-table div {
  color: #ffffff !important;
}

/* 覆盖所有 Tailwind 灰色文字类 */
body.dark-theme .text-gray-900,
body.dark-theme .text-gray-800,
body.dark-theme .text-gray-700,
body.dark-theme .text-gray-600,
body.dark-theme .text-gray-500 {
  color: #ffffff !important;
}

/* 右上角工具栏 */
.right-top {
  position: fixed;
  right: 10px;
  top: 0;
  color: #808080;
  display: flex;
  justify-content: flex-end;
  z-index: 1001;
  left: 0;
  cursor: default;
  padding-top: 6px;
  pointer-events: none;
}

.right-top > * {
  pointer-events: auto;
}

.right-top .iconfont {
  font-size: 16px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.right-top .iconfont:hover {
  background-color: #efefef;
  cursor: pointer;
}

/* 主题切换按钮 */
.theme-switch {
  z-index: 9999;
  display: flex;
  align-items: center;
  margin-right: 12px;
  padding: 0 12px 0 6px;
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.theme-switch::after {
  content: "";
  width: 1px;
  height: 16px;
  background: #e6e6e6;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.theme-switch:hover {
  background: #efefef;
}

body.dark-theme .theme-switch:hover {
  background-color: #3a3a3a;
}

body.dark-theme .right-top {
  color: #e0e0e0;
}

body.dark-theme .right-top .iconfont:hover {
  background-color: #3a3a3a;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0 !important;
    padding-left: 8px;
    padding-right: 8px;
  }

  .main-content.collapsed {
    margin-left: 0 !important;
  }

  .layui-col-md3,
  .layui-col-md4,
  .layui-col-md5,
  .layui-col-md6,
  .layui-col-md8 {
    width: 100% !important;
    margin-bottom: 10px;
  }

  .layui-col-space10 > * {
    padding: 5px 2px;
  }

  .layui-col-space15 > * {
    padding: 7.5px 2px;
  }
}



.table-card  {
    padding: 16px 16px 0px 16px !important;
}