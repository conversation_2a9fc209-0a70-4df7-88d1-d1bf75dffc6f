# Dashboard 图表动态更新例子

## 1. 查看当前数据

在浏览器控制台中输入以下命令查看当前的图表数据：

```javascript
// 查看当前图表数据
console.log(window.dashboard_chartData);
```

## 2. 更新问题分析图表数据

### 方法一：更新整个 chartData 对象

```javascript
// 完整更新图表数据
window.dashboard_chartData = {
  problemChart: {
    categories: ['昨日', '今日'],
    totalData: [400, 500],    // 新的总计数据
    eventData: [300, 250],    // 新的事件名称数据
    data: [
      {
        name: '昨日',
        eventTotal: 400,
        topEventName: '商品咨询',
        topEventCount: 300
      },
      {
        name: '今日',
        eventTotal: 500,
        topEventName: '物流查询',
        topEventCount: 250
      }
    ]
  },
  usageChart: {
    xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
    yData: [150, 220, 180, 90, 80, 120, 140, 200]
  }
};

// 调用更新函数
window.updateDashboardData();
```

### 方法二：只更新问题分析图表

```javascript
// 只更新问题分析图表的数据
window.dashboard_chartData.problemChart.totalData = [600, 700];
window.dashboard_chartData.problemChart.eventData = [450, 380];

// 调用更新函数
window.updateDashboardData();
```

### 方法三：只更新用量分析图表

```javascript
// 只更新用量分析图表
window.dashboard_chartData.usageChart.xData = ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00'];
window.dashboard_chartData.usageChart.yData = [80, 120, 200, 300, 250, 180, 150, 100];

// 调用更新函数
window.updateDashboardData();
```

## 3. 测试例子

### 例子1：模拟高峰期数据

```javascript
window.dashboard_chartData = {
  problemChart: {
    categories: ['昨日', '今日'],
    totalData: [800, 1200],    // 高峰期总计
    eventData: [600, 900],     // 高峰期事件数据
    data: [
      {
        name: '昨日',
        eventTotal: 800,
        topEventName: '双11咨询',
        topEventCount: 600
      },
      {
        name: '今日',
        eventTotal: 1200,
        topEventName: '促销活动',
        topEventCount: 900
      }
    ]
  },
  usageChart: {
    xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
    yData: [300, 450, 600, 800, 750, 500, 350, 200]
  }
};
window.updateDashboardData();
```

### 例子2：模拟低峰期数据

```javascript
window.dashboard_chartData = {
  problemChart: {
    categories: ['昨日', '今日'],
    totalData: [150, 180],     // 低峰期总计
    eventData: [80, 120],      // 低峰期事件数据
    data: [
      {
        name: '昨日',
        eventTotal: 150,
        topEventName: '常规咨询',
        topEventCount: 80
      },
      {
        name: '今日',
        eventTotal: 180,
        topEventName: '基础服务',
        topEventCount: 120
      }
    ]
  },
  usageChart: {
    xData: ['02:00', '03:00', '04:00', '05:00', '06:00', '07:00', '08:00', '09:00'],
    yData: [20, 15, 10, 25, 40, 60, 80, 100]
  }
};
window.updateDashboardData();
```

### 例子3：随机数据生成器

```javascript
// 生成随机数据的函数
function generateRandomData() {
  const totalData = [
    Math.floor(Math.random() * 500) + 200,  // 200-700之间
    Math.floor(Math.random() * 500) + 200
  ];
  
  const eventData = [
    Math.floor(Math.random() * 300) + 100,  // 100-400之间
    Math.floor(Math.random() * 300) + 100
  ];
  
  const usageData = Array.from({length: 8}, () => 
    Math.floor(Math.random() * 200) + 50   // 50-250之间
  );
  
  window.dashboard_chartData = {
    problemChart: {
      categories: ['昨日', '今日'],
      totalData: totalData,
      eventData: eventData,
      data: [
        {
          name: '昨日',
          eventTotal: totalData[0],
          topEventName: '随机事件A',
          topEventCount: eventData[0]
        },
        {
          name: '今日',
          eventTotal: totalData[1],
          topEventName: '随机事件B',
          topEventCount: eventData[1]
        }
      ]
    },
    usageChart: {
      xData: ['18:00', '19:00', '20:00', '21:00', '22:00', '23:00', '00:00', '01:00'],
      yData: usageData
    }
  };
  
  window.updateDashboardData();
  console.log('随机数据已生成并更新！');
}

// 调用随机数据生成器
generateRandomData();
```

### 例子4：定时自动更新

```javascript
// 每5秒自动更新一次数据
let autoUpdateInterval = setInterval(() => {
  const totalData = [
    Math.floor(Math.random() * 400) + 300,
    Math.floor(Math.random() * 400) + 300
  ];
  
  const eventData = [
    Math.floor(Math.random() * 200) + 150,
    Math.floor(Math.random() * 200) + 150
  ];
  
  window.dashboard_chartData.problemChart.totalData = totalData;
  window.dashboard_chartData.problemChart.eventData = eventData;
  
  window.updateDashboardData();
  console.log('自动更新:', new Date().toLocaleTimeString(), totalData, eventData);
}, 5000);

// 停止自动更新
// clearInterval(autoUpdateInterval);
```

## 4. 数据结构说明

```javascript
window.dashboard_chartData = {
  problemChart: {
    categories: ['昨日', '今日'],        // Y轴分类标签
    totalData: [数值1, 数值2],          // "总计"系列的数据
    eventData: [数值1, 数值2],          // "事件名称"系列的数据
    data: [                            // 详细数据（暂时未在新图表中使用）
      {
        name: '昨日',
        eventTotal: 数值,
        topEventName: '事件名称',
        topEventCount: 数值
      },
      // ...
    ]
  },
  usageChart: {
    xData: ['时间1', '时间2', ...],     // X轴时间标签
    yData: [数值1, 数值2, ...]          // 用量数据
  }
}
```

## 5. 注意事项

1. **数据更新后必须调用** `window.updateDashboardData()` 才能看到图表更新
2. **totalData 和 eventData** 数组长度必须与 categories 数组长度一致（当前是2个元素）
3. **usageChart 的 xData 和 yData** 数组长度必须一致
4. 所有数值必须是数字类型，不能是字符串
5. 如果只想更新部分数据，可以直接修改对应的属性，然后调用更新函数

## 6. 调试技巧

```javascript
// 查看当前数据
console.log('当前图表数据:', window.dashboard_chartData);

// 查看更新函数是否存在
console.log('更新函数:', typeof window.updateDashboardData);

// 测试更新函数
if (typeof window.updateDashboardData === 'function') {
  window.updateDashboardData();
  console.log('更新函数调用成功');
} else {
  console.error('更新函数不存在');
}
```
